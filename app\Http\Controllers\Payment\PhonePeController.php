<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CombinedOrder;
use App\Models\CustomerPackage;
use App\Models\SellerPackage;
use App\Models\BusinessSetting;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\SellerPackageController;
use App\Http\Controllers\WalletController;
use Session;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class PhonepeController extends Controller
{
    private $clientId;
    private $clientVersion;
    private $clientSecret;
    private $apiEndpoint;
    private $authEndpoint;
    private $accessToken;
    private $tokenExpiresAt;
    private $isSandbox;

    public function __construct()
    {
        $this->clientId = env('PHONEPE_CLIENT_ID');
        $this->clientVersion = env('PHONEPE_CLIENT_VERSION');
        $this->clientSecret = env('PHONEPE_CLIENT_SECRET');

        // Debug log to check if values are loaded
        Log::info('PhonePe Constructor Values: ', [
            'clientId' => $this->clientId,
            'clientSecret' => $this->clientSecret ? 'SET' : 'NOT SET',
            'clientVersion' => $this->clientVersion
        ]);

        // Set API endpoint based on sandbox mode
        $sandboxSetting = BusinessSetting::where('type', 'phonepe_sandbox')->first();
        $this->isSandbox = $sandboxSetting ? $sandboxSetting->value == 1 : true;

        if ($this->isSandbox) {
            $this->apiEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox';
            $this->authEndpoint = 'https://api-preprod.phonepe.com/apis/pg-sandbox/v1/oauth/token';
        } else {
            $this->apiEndpoint = 'https://api.phonepe.com/apis/pg';
            $this->authEndpoint = 'https://api.phonepe.com/apis/identity-manager/v1/oauth/token';
        }
            $this->apiEndpoint = 'https://api.phonepe.com/apis/pg';
            $this->authEndpoint = 'https://api.phonepe.com/apis/identity-manager/v1/oauth/token';
        // Validate required credentials
        if (!$this->clientId || !$this->clientSecret) {
            Log::error('PhonePe credentials missing: ', [
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret ? 'SET' : 'NOT SET'
            ]);
        }
    }

    /**
     * Check if request is from mobile app
     */
    private function isMobileApp(Request $request = null)
    {
        if (!$request) {
            $request = request();
        }

        $userAgent = $request->header('User-Agent', '');

        Log::info('PhonePe Mobile Detection: ' . json_encode([
            'userAgent' => $userAgent,
            'headers' => $request->headers->all()
        ]));

        // Check for common mobile app indicators
        $mobileIndicators = [
            'Mobile/',
            'Android',
            'iPhone',
            'iPad',
            'Mobile Safari',
            'wv)', // WebView indicator
            'Version/', // Mobile browser version
            'App/', // Generic app indicator
        ];

        foreach ($mobileIndicators as $indicator) {
            if (strpos($userAgent, $indicator) !== false) {
                Log::info('PhonePe Mobile Detected via indicator: ' . $indicator);
                return true;
            }
        }

        // Check if it's a mobile device
        $isMobile = preg_match('/Mobile|Android|iPhone|iPad/', $userAgent);

        Log::info('PhonePe Mobile Detection Result: ' . ($isMobile ? 'true' : 'false'));

        return $isMobile;
    }

    /**
     * Get OAuth token from PhonePe
     */
    private function getAuthToken()
    {
        // Check if we already have a valid token
        if ($this->accessToken && $this->tokenExpiresAt && $this->tokenExpiresAt > time()) {
            return $this->accessToken;
        }

        try {
            // Request new token
            $response = Http::asForm()->post($this->authEndpoint, [
                'client_id' => $this->clientId,
                'client_version' => $this->clientVersion,
                'client_secret' => $this->clientSecret,
                'grant_type' => 'client_credentials'
            ]);

            $data = $response->json();

            Log::info('PhonePe Auth Response: ' . json_encode($data));

            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                $this->tokenExpiresAt = time() + ($data['expires_in'] ?? 3600);
                return $this->accessToken;
            }

            Log::error('PhonePe Auth Failed: ' . json_encode($data));
            return null;
        } catch (\Exception $e) {
            Log::error('PhonePe Auth Exception: ' . $e->getMessage());
            return null;
        }
    }

    public function pay(Request $request = null)
    {
        try {
            // Validate PhonePe credentials
            if (!$this->clientId || !$this->clientSecret) {
                Log::error('PhonePe credentials not configured properly');
                flash(translate('Payment gateway not configured properly'))->error();
                return redirect()->route('home');
            }

            if (!Session::has('payment_type')) {
                flash(translate('Payment session expired'))->error();
                return redirect()->route('home');
            }

            $paymentType = Session::get('payment_type');
            $amount = 0;
            $transactionId = 'TX' . time() . rand(1000, 9999);

            // Store transaction ID in session for callback verification
            Session::put('phonepe_transaction_id', $transactionId);

            switch ($paymentType) {
                case 'cart_payment':
                    $combined_order = CombinedOrder::findOrFail(Session::get('combined_order_id'));
                    $amount = round($combined_order->grand_total * 100); // Amount in paise
                    break;

                case 'wallet_payment':
                    $amount = round(Session::get('payment_data')['amount'] * 100); // Amount in paise
                    break;

                case 'customer_package_payment':
                    $customer_package = CustomerPackage::findOrFail(Session::get('payment_data')['customer_package_id']);
                    $amount = round($customer_package->amount * 100); // Amount in paise
                    break;

                case 'seller_package_payment':
                    $seller_package = SellerPackage::findOrFail(Session::get('payment_data')['seller_package_id']);
                    $amount = round($seller_package->amount * 100); // Amount in paise
                    break;

                default:
                    flash(translate('Invalid payment type'))->error();
                    return redirect()->route('home');
            }

            // Create payment request
            $response = $this->initiatePayment($amount, $transactionId, $request);

            if (isset($response['redirectUrl']) && isset($response['orderId'])) {
                $redirectUrl = $response['redirectUrl'];
                $orderId = $response['orderId'];

                // For mobile apps, show a custom page that handles app launching
                if ($this->isMobileApp($request)) {
                    // Create UPI intent URLs for mobile apps
                    $amountInRupees = $amount / 100;
                    $phonepeIntentUrl = 'phonepe://pay?pa=cloudmart@paytm&pn=CloudMart&am=' . $amountInRupees . '&cu=INR&tn=Order-' . $orderId;

                    return view('frontend.payment.phonepe_mobile', [
                        'intentUrl' => $phonepeIntentUrl,
                        'redirectUrl' => $redirectUrl,
                        'orderId' => $orderId,
                        'amount' => $amount/100
                    ]);
                }

                return redirect($redirectUrl);
            } else {
                $errorMessage = isset($response['message']) ? $response['message'] : 'Payment initiation failed';
                Log::error('PhonePe Payment Error: ' . json_encode($response));
                flash(translate('Payment failed: ' . $errorMessage))->error();
                return redirect()->route('home');
            }

        } catch (\Exception $e) {
            Log::error('PhonePe Payment Exception: ' . $e->getMessage());
            flash(translate('Something went wrong during payment'))->error();
            return redirect()->route('home');
        }
    }

    private function initiatePayment($amount, $transactionId, Request $request = null)
    {
        try {
            // Get auth token
            $token = $this->getAuthToken();
            if (!$token) {
                return ['error' => 'Failed to get authentication token'];
            }

            $callbackUrl = route('phonepe.callback');
            $isMobile = $this->isMobileApp($request);

            // Use standard PG_CHECKOUT flow for all devices
            // The mobile handling will be done in the response processing
            $payload = [
                'merchantOrderId' => $transactionId,
                'amount' => $amount,
                'expireAfter' => 1200, // 20 minutes
                'paymentFlow' => [
                    'type' => 'PG_CHECKOUT',
                    'message' => 'Payment for order',
                    'merchantUrls' => [
                        'redirectUrl' => $callbackUrl
                    ]
                ]
            ];

            // Make API request
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'O-Bearer ' . $token
            ])->post($this->apiEndpoint . '/checkout/v2/pay', $payload);

            $responseData = $response->json();

            Log::info('PhonePe Payment Request: ' . json_encode([
                'payload' => $payload,
                'response' => $responseData,
                'isMobile' => $isMobile
            ]));

            // For mobile apps, also extract intent URL if available
            if ($isMobile && isset($responseData['data']['intentUrl'])) {
                $responseData['intentUrl'] = $responseData['data']['intentUrl'];
            }

            return $responseData;

        } catch (\Exception $e) {
            Log::error('PhonePe Payment Initiation Error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function callback(Request $request)
    {
        try {
            Log::info('PhonePe Callback Request: ' . json_encode($request->all()));

            // Handle AJAX status check requests from mobile view
            if ($request->has('check_status') && $request->input('check_status')) {
                $orderId = $request->input('orderId');
                if (!$orderId) {
                    return response()->json(['success' => false, 'message' => 'Order ID missing']);
                }

                $response = $this->checkPaymentStatus($orderId);

                if (isset($response['state']) && $response['state'] === 'COMPLETED') {
                    return response()->json(['success' => true, 'status' => 'completed']);
                } else {
                    return response()->json(['success' => false, 'status' => 'pending']);
                }
            }

            // Get order ID from request or session
            $orderId = $request->input('orderId') ?: $request->input('order_id');

            if (!$orderId) {
                // Try to get transaction ID from session as fallback
                $transactionId = Session::get('phonepe_transaction_id');
                if ($transactionId) {
                    // For OAuth flow, we need to use the orderId, not transactionId
                    Log::info('PhonePe Callback: Using session transaction ID as fallback: ' . $transactionId);
                    $orderId = $transactionId; // This might not work, but we'll try
                }
            }

            if (!$orderId) {
                Log::error('PhonePe Callback: No order ID found in request: ' . json_encode($request->all()));
                flash(translate('Invalid payment response'))->error();
                return redirect()->route('home');
            }

            // Verify payment status
            $response = $this->checkPaymentStatus($orderId);

            Log::info('PhonePe Status Check Response: ' . json_encode($response));

            if (isset($response['state']) && $response['state'] === 'COMPLETED') {

                $payment_details = json_encode($response);

                // Clear the transaction ID from session
                Session::forget('phonepe_transaction_id');

                if (Session::has('payment_type')) {
                    $paymentType = Session::get('payment_type');

                    switch ($paymentType) {
                        case 'cart_payment':
                            return (new CheckoutController)->checkout_done(Session::get('combined_order_id'), $payment_details);

                        case 'wallet_payment':
                            return (new WalletController)->wallet_payment_done(Session::get('payment_data'), $payment_details);

                        case 'customer_package_payment':
                            return (new CustomerPackageController)->purchase_payment_done(Session::get('payment_data'), $payment_details);

                        case 'seller_package_payment':
                            return (new SellerPackageController)->purchase_payment_done(Session::get('payment_data'), $payment_details);
                    }
                }
            }

            $errorMessage = isset($response['message']) ? $response['message'] : 'Payment verification failed';
            Log::error('PhonePe Payment Failed: ' . $errorMessage);
            flash(translate('Payment failed: ' . $errorMessage))->error();
            return redirect()->route('home');

        } catch (\Exception $e) {
            Log::error('PhonePe Callback Exception: ' . $e->getMessage());
            flash(translate('Payment verification failed'))->error();
            return redirect()->route('home');
        }
    }

    private function checkPaymentStatus($orderId)
    {
        try {
            // Get auth token
            $token = $this->getAuthToken();
            if (!$token) {
                return ['error' => 'Failed to get authentication token'];
            }

            // Make API request
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'O-Bearer ' . $token
            ])->get($this->apiEndpoint . '/checkout/v2/order/' . $orderId);

            $responseData = $response->json();

            Log::info('PhonePe Status Check: ' . json_encode([
                'orderId' => $orderId,
                'response' => $responseData
            ]));

            return $responseData;

        } catch (\Exception $e) {
            Log::error('PhonePe Status Check Error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Handle PhonePe webhook notifications
     */
    public function webhook(Request $request)
    {
        try {
            Log::info('PhonePe Webhook Request: ' . json_encode($request->all()));

            // Get the payment data from webhook
            $paymentData = $request->all();

            // Log the webhook for debugging
            Log::info('PhonePe Webhook Data: ' . json_encode($paymentData));

            // Return success response to PhonePe
            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('PhonePe Webhook Exception: ' . $e->getMessage());
            return response('Error', 500);
        }
    }

    public function credentials_index()
    {
        return view('backend.setup_configurations.phonepe_credential');
    }

    public function update_credentials(Request $request)
    {
        $request->validate([
            'PHONEPE_CLIENT_ID' => 'required|string',
            'PHONEPE_CLIENT_SECRET' => 'required|string',
            'PHONEPE_CLIENT_VERSION' => 'required|string'
        ]);

        foreach ($request->types as $key => $type) {
            $this->overWriteEnvFile($type, $request[$type]);
        }

        $business_settings = BusinessSetting::where('type', 'phonepe_sandbox')->first();

        if ($business_settings != null) {
            if ($request->has('phonepe_sandbox')) {
                $business_settings->value = 1;
                $business_settings->save();
            } else {
                $business_settings->value = 0;
                $business_settings->save();
            }
        } else {
            $business_settings = new BusinessSetting;
            $business_settings->type = 'phonepe_sandbox';
            $business_settings->value = $request->has('phonepe_sandbox') ? 1 : 0;
            $business_settings->save();
        }

        flash(translate("PhonePe settings updated successfully"))->success();
        return back();
    }

    private function overWriteEnvFile($type, $val)
    {
        $path = base_path('.env');
        if (file_exists($path)) {
            $val = '"'.trim($val).'"';
            if(is_numeric(strpos(file_get_contents($path), $type)) && strpos(file_get_contents($path), $type) >= 0){
                file_put_contents($path, str_replace(
                    $type.'="'.env($type).'"', $type.'='.$val, file_get_contents($path)
                ));
            }
            else{
                file_put_contents($path, file_get_contents($path)."\r\n".$type.'='.$val);
            }
        }
    }
}
